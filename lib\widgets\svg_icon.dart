// lib/widgets/svg_icon.dart
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A reusable widget for displaying SVG icons with consistent styling
class SvgIcon extends StatelessWidget {
  final String assetPath;
  final double? width;
  final double? height;
  final Color? color;
  final BoxFit fit;
  final IconData? fallbackIcon;

  const SvgIcon({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.color,
    this.fit = BoxFit.contain,
    this.fallbackIcon,
  });

  /// Factory constructor for category icons with standard sizing
  factory SvgIcon.category({
    required String categoryName,
    double size = 35,
    Color color = Colors.white,
  }) {
    return SvgIcon(
      assetPath: 'assets/icons/$categoryName.svg',
      width: size,
      height: size,
      color: color,
      fallbackIcon: _getCategoryFallbackIcon(categoryName),
    );
  }

  /// Get fallback icon for category names
  static IconData _getCategoryFallbackIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'bold':
        return Icons.flash_on;
      case 'bad':
        return Icons.thumb_down;
      case 'cute':
        return Icons.favorite;
      case 'clever':
      case 'cleaver':
        return Icons.lightbulb;
      case 'genius':
        return Icons.psychology;
      case 'dirty':
        return Icons.whatshot;
      case 'flirty':
        return Icons.face;
      case 'hookup':
        return Icons.nightlife;
      case 'romantic':
        return Icons.favorite_border;
      case 'funny':
        return Icons.emoji_emotions;
      case 'nerd':
        return Icons.science;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      assetPath,
      width: width,
      height: height,
      fit: fit,
      colorFilter: color != null 
          ? ColorFilter.mode(color!, BlendMode.srcIn) 
          : null,
      placeholderBuilder: (context) => Icon(
        fallbackIcon ?? Icons.image,
        size: width ?? height ?? 24,
        color: color,
      ),
    );
  }
}
