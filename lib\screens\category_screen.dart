// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/category_card.dart';
import '../providers/theme_provider.dart';

final categories = [
  {
    'name': 'Bold',
    'iconAsset': 'assets/icons/bold.png',
    'color': Colors.deepOrange,
  },
  {'name': 'Bad', 'icon': Icons.thumb_down, 'color': Colors.blueGrey},
  {
    'name': 'Cute',
    'iconAsset': 'assets/icons/cute.png',
    'color': Colors.pinkAccent,
  },
  {
    'name': 'Clever',
    'iconAsset': 'assets/icons/cleaver.png',
    'color': Colors.purple,
  },
  {
    'name': 'Genius',
    'iconAsset': 'assets/icons/genius.png',
    'color': Colors.indigo,
  },
  {
    'name': 'Dirty',
    'iconAsset': 'assets/icons/dirty.png',
    'color': Colors.green,
  },
  {
    'name': 'Flirty',
    'iconAsset': 'assets/icons/flirty.png',
    'color': Colors.redAccent,
  },
  {
    'name': 'Hookup',
    'iconAsset': 'assets/icons/hookup.png',
    'color': Colors.teal,
  },
  {
    'name': 'Romantic',
    'iconAsset': 'assets/icons/romantic.png',
    'color': Colors.red,
  },
  {
    'name': 'Funny',
    'iconAsset': 'assets/icons/funny.png',
    'color': Colors.amber,
  },
  {'name': 'Nerd', 'iconAsset': 'assets/icons/nerd.png', 'color': Colors.cyan},
  {
    'name': 'Food',
    'iconAsset': 'assets/icons/food.png',
    'color': Colors.orange,
  },
];

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode
                ? Colors.white
                : Colors.black,
            elevation: 0,
            title: Text('Pickup Lines'),
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(Icons.menu),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.favorite),
                onPressed: () => Navigator.pushNamed(context, '/favorites'),
              ),
            ],
          ),
          drawer: AppDrawer(),
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final cat = categories[index];
                return CategoryCard(
                  title: cat['name'] as String,
                  icon: cat['icon'] as IconData?,
                  iconAsset: cat['iconAsset'] as String?,
                  color: cat['color'] as Color,
                  onTap: () => Navigator.pushNamed(
                    context,
                    '/lines',
                    arguments: cat['name'] as String,
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
