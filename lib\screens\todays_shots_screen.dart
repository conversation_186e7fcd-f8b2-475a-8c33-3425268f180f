// lib/screens/todays_shots_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import '../utils/image_cache_manager.dart';
import '../widgets/app_drawer.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';

class TodaysShotsScreen extends StatefulWidget {
  const TodaysShotsScreen({super.key});

  @override
  State<TodaysShotsScreen> createState() => _TodaysShotsScreenState();
}

class _TodaysShotsScreenState extends State<TodaysShotsScreen> {
  final Random _random = Random();

  // Random pickup lines collection from all categories
  List<String> _pickupLines = [];

  // Background images from 0.png to 10.png
  final List<String> backgroundImages = [
    'assets/images/0.png',
    'assets/images/1.png',
    'assets/images/2.png',
    'assets/images/3.png',
    'assets/images/4.png',
    'assets/images/5.png',
    'assets/images/6.png',
    'assets/images/7.png',
    'assets/images/8.png',
    'assets/images/9.png',
    'assets/images/10.png',
  ];

  String _currentLine = "";
  String _currentBackground = "";

  @override
  void initState() {
    super.initState();
    // Load pickup lines from all categories
    _loadAllPickupLines();
    _generateRandomShot();
  }

  void _loadAllPickupLines() {
    _pickupLines.clear();
    // Get all categories and combine their lines
    final categories = PickupLinesData.getAvailableCategories();
    for (String category in categories) {
      _pickupLines.addAll(
        PickupLinesData.getLinesForCategory(category, 'English'),
      );
    }
  }

  void _generateRandomShot() {
    setState(() {
      _currentLine = _pickupLines[_random.nextInt(_pickupLines.length)];
      _currentBackground =
          backgroundImages[_random.nextInt(backgroundImages.length)];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: AppBar(
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode
                ? Colors.white
                : Colors.black,
            elevation: 0,
            title: Text(
              "Today's Shots",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(
                  Icons.menu,
                  color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                ),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.refresh, color: Colors.black),
                onPressed: _generateRandomShot,
                tooltip: 'Get New Shot',
              ),
            ],
          ),
          drawer: AppDrawer(),
          body: Container(
            color: Colors.white,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  // Make post reactive - change background and play sound
                  _generateRandomShot();
                  if (favoritesProvider.tapSoundEnabled) {
                    SystemSound.play(SystemSoundType.click);
                  }
                },
                child: Container(
                  margin: EdgeInsets.all(16),
                  height: 500,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        spreadRadius: 0,
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: Stack(
                      children: [
                        // Background Image
                        Positioned.fill(
                          child: OptimizedAssetImage(
                            imagePath: _currentBackground,
                            fit: BoxFit.cover,
                            filterQuality: FilterQuality.low,
                            errorWidget: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.deepPurple.shade400,
                                    Colors.purple.shade600,
                                    Colors.pink.shade500,
                                  ],
                                ),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.favorite,
                                  color: Colors.white54,
                                  size: 48,
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Dark overlay for better text readability
                        Container(
                          width: double.infinity,
                          height: double.infinity,
                          color: Colors.black.withValues(alpha: 0.4),
                        ),
                        // Content
                        Positioned.fill(
                          child: Container(
                            padding: EdgeInsets.all(24),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                // Header
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: 16,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.auto_awesome,
                                        color: Colors.orange,
                                        size: 20,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        "Today's Random Shot",
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Quote content
                                Expanded(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 20,
                                      horizontal: 16,
                                    ),
                                    child: Center(
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          style: TextStyle(
                                            fontSize: 20,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            height: 1.4,
                                            shadows: [
                                              Shadow(
                                                color: Colors.black.withValues(
                                                  alpha: 0.5,
                                                ),
                                                offset: Offset(1, 1),
                                                blurRadius: 2,
                                              ),
                                            ],
                                          ),
                                          children: [
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize: 28,
                                                fontWeight: FontWeight.w900,
                                              ),
                                            ),
                                            TextSpan(text: _currentLine),
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize: 28,
                                                fontWeight: FontWeight.w900,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // Action buttons
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      _buildActionButton(
                                        icon: Icons.refresh,
                                        label: 'New',
                                        onTap: _generateRandomShot,
                                      ),
                                      Builder(
                                        builder: (context) {
                                          final postId =
                                              'today_${_currentLine.hashCode}';
                                          final isFavorite = favoritesProvider
                                              .isFavorite(postId);
                                          return _buildActionButton(
                                            icon: isFavorite
                                                ? Icons.favorite
                                                : Icons.favorite_border,
                                            label: isFavorite
                                                ? 'Liked'
                                                : 'Like',
                                            onTap: () {
                                              favoritesProvider.toggleFavorite(
                                                postId,
                                                _currentLine,
                                                _currentBackground,
                                              );
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    isFavorite
                                                        ? 'Removed from favorites!'
                                                        : 'Added to favorites!',
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.copy,
                                        label: 'Copy',
                                        onTap: () {
                                          Clipboard.setData(
                                            ClipboardData(text: _currentLine),
                                          );
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Copied to clipboard!',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.share,
                                        label: 'Share',
                                        onTap: () {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Share functionality',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.black, size: 24),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
