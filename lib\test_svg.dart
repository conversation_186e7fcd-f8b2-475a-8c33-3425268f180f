// Test file to verify SVG loading
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TestSvgScreen extends StatelessWidget {
  const TestSvgScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('SVG Test')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Testing SVG Icons:'),
            SizedBox(height: 20),
            // Test direct SvgPicture.asset
            SvgPicture.asset(
              'assets/icons/bold.svg',
              width: 50,
              height: 50,
              colorFilter: ColorFilter.mode(Colors.red, BlendMode.srcIn),
            ),
            SizedBox(height: 20),
            SvgPicture.asset(
              'assets/icons/cute.svg',
              width: 50,
              height: 50,
              colorFilter: ColorFilter.mode(Colors.blue, BlendMode.srcIn),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            SvgPicture.asset(
              'assets/icons/food.svg',
              width: 50,
              height: 50,
              colorFilter: ColorFilter.mode(Colors.green, BlendMode.srcIn),
            ),
          ],
        ),
      ),
    );
  }
}
