Stack trace:
Frame         Function      Args
0007FFFFAA90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9990) msys-2.0.dll+0x1FE8E
0007FFFFAA90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAD68) msys-2.0.dll+0x67F9
0007FFFFAA90  000210046832 (000210286019, 0007FFFFA948, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAA90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAA90  000210068E24 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAD70  00021006A225 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF826060000 ntdll.dll
7FF8248D0000 KERNEL32.DLL
7FF8233C0000 KERNELBASE.dll
7FF824190000 USER32.dll
7FF823250000 win32u.dll
000210040000 msys-2.0.dll
7FF8247F0000 GDI32.dll
7FF823B40000 gdi32full.dll
7FF823280000 msvcp_win.dll
7FF8237B0000 ucrtbase.dll
7FF824040000 advapi32.dll
7FF825360000 msvcrt.dll
7FF823F90000 sechost.dll
7FF8244E0000 RPCRT4.dll
7FF8226C0000 CRYPTBASE.DLL
7FF8231B0000 bcryptPrimitives.dll
7FF823F50000 IMM32.DLL
