// lib/providers/favorites_provider.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FavoritePost {
  final String id;
  final String text;
  final String backgroundImage;
  final DateTime addedAt;

  FavoritePost({
    required this.id,
    required this.text,
    required this.backgroundImage,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'backgroundImage': backgroundImage,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory FavoritePost.fromJson(Map<String, dynamic> json) {
    return FavoritePost(
      id: json['id'],
      text: json['text'],
      backgroundImage: json['backgroundImage'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }
}

class FavoritesProvider extends ChangeNotifier {
  final List<FavoritePost> _favorites = [];
  bool _tapSoundEnabled = true;

  List<FavoritePost> get favorites => List.unmodifiable(_favorites);
  bool get tapSoundEnabled => _tapSoundEnabled;
  int get favoritesCount => _favorites.length;

  void setTapSoundEnabled(bool enabled) {
    _tapSoundEnabled = enabled;
    notifyListeners();
  }

  bool isFavorite(String postId) {
    return _favorites.any((post) => post.id == postId);
  }

  void addToFavorites(String postId, String text, String backgroundImage) {
    // Check if already in favorites
    if (isFavorite(postId)) {
      return;
    }

    final favoritePost = FavoritePost(
      id: postId,
      text: text,
      backgroundImage: backgroundImage,
      addedAt: DateTime.now(),
    );

    _favorites.insert(0, favoritePost); // Add to beginning for recent first
    _playTapSound();
    notifyListeners();
  }

  void removeFromFavorites(String postId) {
    _favorites.removeWhere((post) => post.id == postId);
    _playTapSound();
    notifyListeners();
  }

  void toggleFavorite(String postId, String text, String backgroundImage) {
    if (isFavorite(postId)) {
      removeFromFavorites(postId);
    } else {
      addToFavorites(postId, text, backgroundImage);
    }
  }

  void clearAllFavorites() {
    _favorites.clear();
    notifyListeners();
  }

  List<FavoritePost> getFavoritesByDate() {
    final sortedFavorites = List<FavoritePost>.from(_favorites);
    sortedFavorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    return sortedFavorites;
  }

  void _playTapSound() {
    if (_tapSoundEnabled) {
      try {
        SystemSound.play(SystemSoundType.click);
      } catch (e) {
        // Ignore sound errors
        debugPrint('Sound play error: $e');
      }
    }
  }

  // Get favorites for a specific time period
  List<FavoritePost> getFavoritesFromLastDays(int days) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _favorites
        .where((post) => post.addedAt.isAfter(cutoffDate))
        .toList();
  }

  // Search favorites by text
  List<FavoritePost> searchFavorites(String query) {
    if (query.isEmpty) return favorites;

    return _favorites
        .where((post) => post.text.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
}
