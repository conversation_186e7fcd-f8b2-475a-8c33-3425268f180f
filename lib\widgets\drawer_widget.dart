// lib/widgets/drawer_widget.dart
import 'package:flutter/material.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Colors.teal),
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset('assets/images/charm_logo.png', width: 60),
                SizedBox(height: 10),
                Text(
                  'Charm Shots',
                  style: TextStyle(
                    fontSize: 22,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: Icon(Icons.today),
            title: Text("Today's Shots"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.favorite),
            title: Text("Favourite"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.edit),
            title: Text("Shots Maker"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.settings),
            title: Text("Settings"),
            onTap: () {},
          ),
          Divider(),
          ListTile(
            leading: Icon(Icons.info),
            title: Text("About This App"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.privacy_tip),
            title: Text("Privacy Policy"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.description),
            title: Text("Terms and Conditions"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.star_rate),
            title: Text("Rate Us"),
            onTap: () {},
          ),
          ListTile(
            leading: Icon(Icons.share),
            title: Text("Share App"),
            onTap: () {},
          ),
          Spacer(),
          ListTile(
            title: Text(
              "Version 1.0.0",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
