// lib/utils/performance_test.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'performance_utils.dart';

/// Performance testing utilities for validating optimizations
class PerformanceTest {
  /// Test image loading performance
  static Future<void> testImageLoadingPerformance() async {
    if (!kDebugMode) return;

    debugPrint('🧪 Starting image loading performance test...');

    await PerformanceUtils.measureAsync('Image Loading Test', () async {
      // Simulate loading multiple images
      final futures = <Future>[];
      for (int i = 0; i < 5; i++) {
        futures.add(Future.delayed(Duration(milliseconds: 10 * i)));
      }
      await Future.wait(futures);
    });

    debugPrint('✅ Image loading performance test completed');
  }

  /// Test widget rebuild performance
  static void testWidgetRebuildPerformance() {
    if (!kDebugMode) return;

    debugPrint('🧪 Starting widget rebuild performance test...');

    // Simulate rapid widget rebuilds
    for (int i = 0; i < 10; i++) {
      PerformanceUtils.trackWidgetRebuild('TestWidget');
      // Small delay to simulate real rebuilds
      Future.delayed(Duration(milliseconds: 5));
    }

    debugPrint('✅ Widget rebuild performance test completed');
  }

  /// Test memory usage patterns
  static void testMemoryUsage() {
    if (!kDebugMode) return;

    debugPrint('🧪 Starting memory usage test...');

    // Simulate memory allocation patterns
    final List<List<int>> memoryTest = [];

    PerformanceUtils.measure('Memory Allocation Test', () {
      for (int i = 0; i < 100; i++) {
        memoryTest.add(List.generate(1000, (index) => index));
      }
    });

    PerformanceUtils.logMemoryUsage('After memory allocation test');

    // Clean up
    memoryTest.clear();

    debugPrint('✅ Memory usage test completed');
  }

  /// Run all performance tests
  static Future<void> runAllTests() async {
    if (!kDebugMode) return;

    debugPrint('🚀 Starting comprehensive performance test suite...');

    await testImageLoadingPerformance();
    testWidgetRebuildPerformance();
    testMemoryUsage();

    // Generate final report
    final report = PerformanceUtils.generatePerformanceReport();
    debugPrint('📊 Performance Test Results:\n$report');

    debugPrint('🏁 Performance test suite completed');
  }

  /// Stress test for frame performance
  static void stressTestFramePerformance() {
    if (!kDebugMode) return;

    debugPrint('🧪 Starting frame performance stress test...');

    // Simulate heavy computation that might cause frame drops
    PerformanceUtils.measure('Frame Stress Test', () {
      for (int i = 0; i < 10000; i++) {
        // Simulate expensive operations
        final list = List.generate(100, (index) => index * index);
        list.sort();
      }
    });

    debugPrint('✅ Frame performance stress test completed');
  }

  /// Test tap responsiveness
  static void testTapResponsiveness() {
    if (!kDebugMode) return;

    debugPrint('🧪 Starting tap responsiveness test...');

    final List<Duration> tapTimes = [];

    for (int i = 0; i < 10; i++) {
      final stopwatch = Stopwatch()..start();

      // Simulate tap processing
      Future.delayed(Duration(milliseconds: 1), () {
        stopwatch.stop();
        tapTimes.add(stopwatch.elapsed);
      });
    }

    // Calculate average tap response time
    Future.delayed(Duration(milliseconds: 100), () {
      if (tapTimes.isNotEmpty) {
        final avgTime =
            tapTimes.fold<int>(
              0,
              (sum, duration) => sum + duration.inMicroseconds,
            ) /
            tapTimes.length;
        debugPrint(
          '📱 Average tap response time: ${(avgTime / 1000).toStringAsFixed(2)}ms',
        );
      }
      debugPrint('✅ Tap responsiveness test completed');
    });
  }
}

/// Extension to add performance testing to widgets
extension PerformanceTestWidget on Widget {
  Widget withPerformanceTest(String testName) {
    if (!kDebugMode) return this;

    return Builder(
      builder: (context) {
        PerformanceUtils.trackWidgetRebuild('$testName-Widget');
        return this;
      },
    );
  }
}
