// lib/screens/language_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../providers/theme_provider.dart';

class LanguageSelectScreen extends StatelessWidget {
  const LanguageSelectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode
                ? Colors.white
                : Colors.black,
            elevation: 0,
            title: Text('Charm Shot'),
            leading: Builder(
              builder: (context) => IconButton(
                icon: Icon(Icons.menu),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
          ),
          drawer: AppDrawer(),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Material(
                    elevation: 16,
                    borderRadius: BorderRadius.circular(32),
                    shadowColor: Colors.black.withAlpha(100),
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        minimumSize: Size(340, 100),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      icon: Text('🇮🇳', style: TextStyle(fontSize: 40)),
                      label: Text(
                        'Hindi',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () => Navigator.pushNamed(
                        context,
                        '/categories',
                        arguments: 'Hindi',
                      ),
                    ),
                  ),
                  SizedBox(height: 48),
                  Material(
                    elevation: 16,
                    borderRadius: BorderRadius.circular(32),
                    shadowColor: Colors.black.withAlpha(100),
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        minimumSize: Size(340, 100),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      icon: Text('🇬🇧', style: TextStyle(fontSize: 40)),
                      label: Text(
                        'English',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () => Navigator.pushNamed(
                        context,
                        '/categories',
                        arguments: 'English',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
