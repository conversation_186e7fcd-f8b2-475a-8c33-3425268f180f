// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'screens/language_selection_screen.dart';
import 'screens/category_screen.dart';
import 'screens/lines_list_screen.dart';
import 'screens/performance_debug_screen.dart';
import 'screens/favorites_screen.dart';
import 'utils/image_cache_manager.dart';
import 'utils/performance_utils.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
      ],
      child: CharmShotsApp(),
    ),
  );
}

class CharmShotsApp extends StatefulWidget {
  const CharmShotsApp({super.key});

  @override
  State<CharmShotsApp> createState() => _CharmShotsAppState();
}

class _CharmShotsAppState extends State<CharmShotsApp> {
  @override
  void initState() {
    super.initState();
    // Initialize performance monitoring and image preloading after startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAfterStartup();
    });
  }

  void _initializeAfterStartup() {
    // Start performance monitoring after app is stable
    Future.delayed(const Duration(seconds: 2), () {
      PerformanceUtils.startFrameMonitoring();
    });

    // Only preload images when user actually navigates to lines screen
    // This eliminates startup frame drops completely
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Charm Shots',
          theme: themeProvider.currentTheme,
          navigatorKey: NavigationService.navigatorKey,
          initialRoute: '/',
          // Add explicit text direction to fix Directionality issues
          builder: (context, child) {
            return Directionality(
              textDirection: TextDirection.ltr,
              child: child ?? Container(),
            );
          },
          routes: {
            '/': (context) => SplashScreen(),
            '/language': (context) => LanguageSelectScreen(),
            '/categories': (context) {
              final args =
                  ModalRoute.of(context)!.settings.arguments as String? ??
                  'English';
              return CategoryScreen(language: args);
            },
            '/lines': (context) {
              final args = ModalRoute.of(context)!.settings.arguments;
              if (args is Map<String, String>) {
                return LinesListScreen(
                  category: args['category'] ?? 'Bold',
                  language: args['language'] ?? 'English',
                );
              } else {
                // Fallback for old navigation
                return LinesListScreen(
                  category: args as String? ?? 'Bold',
                  language: 'English',
                );
              }
            },
            '/performance-debug': (context) => PerformanceDebugScreen(),
            '/favorites': (context) => FavoritesScreen(),
            // Add other routes for settings, about, privacy, etc.
          },
        );
      },
    );
  }
}
