// lib/widgets/app_drawer.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../screens/performance_debug_screen.dart';
import '../screens/todays_shots_screen.dart';
import '../screens/shots_maker_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/favorites_screen.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  String _version = "1.0.0";

  @override
  void initState() {
    super.initState();
    _getVersionInfo();
  }

  Future<void> _getVersionInfo() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = "v${packageInfo.version}";
      });
    } catch (e) {
      // Fallback to default version if package info fails
      setState(() {
        _version = "v1.0.0";
      });
    }
  }

  Future<void> _shareApp() async {
    try {
      final String appName = "Charm Shots";
      final String shareText =
          """
🌟 Check out $appName! 🌟

The ultimate pickup lines app with amazing features:
✨ Daily pickup lines
💝 Save your favorites
🎨 Create custom shots
🌙 Dark mode support

Download now and charm your way to success!

#CharmShots #PickupLines #Dating #Romance
""";

      await SharePlus.instance.share(
        ShareParams(text: shareText, subject: 'Check out $appName!'),
      );
    } catch (e) {
      // Fallback share text if package info fails
      await SharePlus.instance.share(
        ShareParams(
          text: "Check out Charm Shots - The ultimate pickup lines app! 🌟",
          subject: 'Check out Charm Shots!',
        ),
      );
    }
  }

  Future<void> _rateApp() async {
    // TODO: Replace this URL with your actual Play Store URL after publishing
    const String playStoreUrl =
        "https://play.google.com/store/apps/details?id=com.example.charmshots.a";

    try {
      final Uri url = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        // Fallback: Show a dialog with instructions
        if (mounted) {
          _showRateAppDialog();
        }
      }
    } catch (e) {
      // Show dialog if URL launch fails
      if (mounted) {
        _showRateAppDialog();
      }
    }
  }

  void _showRateAppDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Rate Charm Shots'),
          content: Text(
            'We would love your feedback! Please rate us on the Google Play Store.\n\n'
            'Search for "Charm Shots" in the Play Store or visit our app page.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Drawer(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade800
              : Colors.white,
          child: Column(
            children: [
              // Upper section with header
              Container(
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode
                      ? Colors.grey.shade800
                      : Colors.white,
                  boxShadow: [
                    // Primary shadow for depth
                    BoxShadow(
                      color: themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.4)
                          : Colors.black.withValues(alpha: 0.12),
                      spreadRadius: 0,
                      blurRadius: 12,
                      offset: Offset(0, 6),
                    ),
                    // Secondary shadow for softer effect
                    BoxShadow(
                      color: themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.06),
                      spreadRadius: 0,
                      blurRadius: 24,
                      offset: Offset(0, 12),
                    ),
                    // Subtle inner highlight
                    BoxShadow(
                      color: themeProvider.isDarkMode
                          ? Colors.white.withValues(alpha: 0.1)
                          : Colors.white.withValues(alpha: 0.8),
                      spreadRadius: 0,
                      blurRadius: 1,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    DrawerHeader(
                      decoration: BoxDecoration(
                        color: themeProvider.isDarkMode
                            ? Colors.grey.shade800
                            : Colors.white,
                        border: Border.all(color: Colors.transparent),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            'assets/images/charm_logo.png',
                            width: 50,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Charm Shots',
                            style: TextStyle(
                              fontSize: 20,
                              color: themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 3D separator line between header and menu items
                    Container(
                      height: 3,
                      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Colors.transparent,
                            Colors.grey.withValues(alpha: 0.15),
                            Colors.grey.withValues(alpha: 0.3),
                            Colors.grey.withValues(alpha: 0.4),
                            Colors.grey.withValues(alpha: 0.3),
                            Colors.grey.withValues(alpha: 0.15),
                            Colors.transparent,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.15),
                            spreadRadius: 0,
                            blurRadius: 3,
                            offset: Offset(0, 1),
                          ),
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.8),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, -1),
                          ),
                        ],
                      ),
                    ),

                    // Main menu items
                    _buildMenuItem(Icons.today, "Today's Shots", () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TodaysShotsScreen(),
                        ),
                      );
                    }, themeProvider),
                    Consumer<FavoritesProvider>(
                      builder: (context, favoritesProvider, child) {
                        return _buildMenuItem(
                          Icons.favorite,
                          "Favourite (${favoritesProvider.favoritesCount})",
                          () {
                            Navigator.pop(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => FavoritesScreen(),
                              ),
                            );
                          },
                          themeProvider,
                        );
                      },
                    ),
                    _buildMenuItem(Icons.edit, "Shots Maker", () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ShotsMakerScreen(),
                        ),
                      );
                    }, themeProvider),
                    _buildMenuItem(Icons.settings, "Settings", () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SettingsScreen(),
                        ),
                      );
                    }, themeProvider),

                    // Add spacing before shadow
                    SizedBox(height: 8),
                  ],
                ),
              ),

              // Lower section with secondary items
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: themeProvider.isDarkMode
                        ? Colors.grey.shade800
                        : Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: themeProvider.isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.08),
                        spreadRadius: 0,
                        blurRadius: 12,
                        offset: Offset(0, -6),
                      ),
                      BoxShadow(
                        color: themeProvider.isDarkMode
                            ? Colors.black.withValues(alpha: 0.2)
                            : Colors.black.withValues(alpha: 0.04),
                        spreadRadius: 0,
                        blurRadius: 24,
                        offset: Offset(0, -12),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Add a subtle inner shadow effect at the top
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              themeProvider.isDarkMode
                                  ? Colors.white.withValues(alpha: 0.05)
                                  : Colors.black.withValues(alpha: 0.03),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 8),
                      _buildMenuItem(
                        Icons.privacy_tip,
                        "Privacy Policy",
                        () {},
                        themeProvider,
                      ),
                      _buildMenuItem(
                        Icons.description,
                        "Terms and Conditions",
                        () {},
                        themeProvider,
                      ),
                      _buildMenuItem(Icons.star_rate, "Rate Us", () {
                        Navigator.pop(context);
                        _rateApp();
                      }, themeProvider),
                      _buildMenuItem(Icons.share, "Share App", () {
                        Navigator.pop(context);
                        _shareApp();
                      }, themeProvider),

                      // Debug section - only show in debug mode
                      if (kDebugMode) ...[
                        SizedBox(height: 16),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16),
                          height: 1,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.transparent,
                                Colors.grey.withValues(alpha: 0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        _buildMenuItem(
                          Icons.bug_report,
                          "Performance Debug",
                          () {
                            Navigator.pop(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PerformanceDebugScreen(),
                              ),
                            );
                          },
                          themeProvider,
                          subtitle: "Debug performance issues",
                          iconColor: Colors.orange,
                        ),
                      ],

                      Spacer(),

                      // Version info
                      Container(
                        padding: EdgeInsets.all(16),
                        child: Text(
                          _version,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: themeProvider.isDarkMode
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMenuItem(
    IconData icon,
    String title,
    VoidCallback onTap,
    ThemeProvider themeProvider, {
    String? subtitle,
    Color? iconColor,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              iconColor ??
              (themeProvider.isDarkMode ? Colors.white70 : Colors.black87),
          size: 24,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: TextStyle(
                  color: themeProvider.isDarkMode
                      ? Colors.grey.shade400
                      : Colors.grey.shade600,
                  fontSize: 12,
                ),
              )
            : null,
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        hoverColor: themeProvider.isDarkMode
            ? Colors.grey.shade700
            : Colors.grey.shade100,
        splashColor: themeProvider.isDarkMode
            ? Colors.grey.shade600
            : Colors.grey.shade200,
      ),
    );
  }
}
