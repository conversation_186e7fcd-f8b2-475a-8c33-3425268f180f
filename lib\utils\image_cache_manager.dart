// lib/utils/image_cache_manager.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class OptimizedImageCache {
  static final OptimizedImageCache _instance = OptimizedImageCache._internal();
  factory OptimizedImageCache() => _instance;
  OptimizedImageCache._internal();

  final Map<String, ImageProvider> _imageCache = {};
  final Map<String, Future<ImageProvider>> _loadingImages = {};
  static const int _maxCacheSize =
      15; // Limit cache size to prevent memory issues

  /// Preload background images to prevent UI blocking
  Future<void> preloadBackgroundImages() async {
    final List<String> backgroundImages = [
      'assets/images/0.png',
      'assets/images/1.png',
      'assets/images/2.png',
      'assets/images/3.png',
      'assets/images/4.png',
      'assets/images/5.png',
      'assets/images/6.png',
      'assets/images/7.png',
      'assets/images/8.png',
      'assets/images/9.png',
      'assets/images/10.png',
    ];

    if (kDebugMode) {
      debugPrint(
        '🖼️ Starting to preload ${backgroundImages.length} background images...',
      );
    }

    // Cache all images instantly without validation to avoid frame drops
    int successCount = 0;
    int failureCount = 0;

    for (String imagePath in backgroundImages) {
      final success = await _preloadSingleImage(imagePath);
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }

      // Minimal delay to prevent blocking
      await Future.delayed(const Duration(milliseconds: 10));
    }

    if (kDebugMode) {
      debugPrint(
        '✅ Image preloading completed: $successCount successful, $failureCount failed',
      );
    }
  }

  Future<bool> _preloadSingleImage(String imagePath) async {
    if (_imageCache.containsKey(imagePath)) return true;

    final stopwatch = Stopwatch()..start();
    try {
      final imageProvider = AssetImage(imagePath);

      // Simply cache the provider - don't try to validate during preload
      // This avoids complex async operations that can cause frame drops
      _imageCache[imagePath] = imageProvider;

      stopwatch.stop();
      if (kDebugMode) {
        debugPrint('✅ Cached $imagePath in ${stopwatch.elapsedMilliseconds}ms');
      }
      return true;
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        debugPrint(
          '❌ Failed to cache image: $imagePath after ${stopwatch.elapsedMilliseconds}ms, error: $e',
        );
      }
      return false;
    }
  }

  /// Get cached image provider or load if not cached
  ImageProvider getCachedImage(String imagePath) {
    if (_imageCache.containsKey(imagePath)) {
      return _imageCache[imagePath]!;
    }

    // Check cache size limit
    if (_imageCache.length >= _maxCacheSize) {
      // Remove oldest entries (simple FIFO strategy)
      final oldestKey = _imageCache.keys.first;
      _imageCache.remove(oldestKey);
    }

    // If not cached, cache it now
    final imageProvider = AssetImage(imagePath);
    _imageCache[imagePath] = imageProvider;
    return imageProvider;
  }

  /// Check if image exists in cache
  bool isImageCached(String imagePath) {
    return _imageCache.containsKey(imagePath);
  }

  /// Clear cache to free memory
  void clearCache() {
    _imageCache.clear();
    _loadingImages.clear();
  }

  /// Get cache size for monitoring
  int get cacheSize => _imageCache.length;
}

/// Navigation service for accessing context
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();
}

/// Optimized Image widget with better performance and fallback
class OptimizedAssetImage extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final FilterQuality filterQuality;
  final Widget? errorWidget;

  const OptimizedAssetImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.filterQuality = FilterQuality.low,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      filterQuality: filterQuality,
      // Use direct asset loading for better reliability
      errorBuilder: (context, error, stackTrace) {
        if (kDebugMode) {
          debugPrint('Failed to load image: $imagePath, error: $error');
        }
        return errorWidget ??
            Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.deepPurple.shade400,
                    Colors.purple.shade600,
                    Colors.pink.shade500,
                  ],
                ),
              ),
              child: Center(
                child: Icon(Icons.favorite, color: Colors.white54, size: 48),
              ),
            );
      },
    );
  }
}
