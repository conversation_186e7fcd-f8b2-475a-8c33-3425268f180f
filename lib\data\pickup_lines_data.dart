// lib/data/pickup_lines_data.dart
// Main controller for pickup lines data
// Imports and manages both English and Hindi pickup lines

import 'english_pickup_lines.dart';
import 'hindi_pickup_lines.dart';

class PickupLinesData {
  // Get lines for a specific category and language
  static List<String> getLinesForCategory(String category, String language) {
    if (language.toLowerCase() == 'english') {
      return EnglishPickupLines.getLinesForCategory(category);
    } else if (language.toLowerCase() == 'hindi') {
      return HindiPickupLines.getLinesForCategory(category);
    }
    // Default to English if language not found
    return EnglishPickupLines.getLinesForCategory(category);
  }

  // Get all available categories (using English as reference)
  static List<String> getAvailableCategories() {
    return EnglishPickupLines.getAvailableCategories();
  }

  // Get all lines from both languages for Today's Shots
  static List<String> getAllLines() {
    List<String> allLines = [];
    final categories = getAvailableCategories();

    // Add English lines
    for (String category in categories) {
      allLines.addAll(EnglishPickupLines.getLinesForCategory(category));
    }

    // Add Hindi lines
    for (String category in categories) {
      allLines.addAll(HindiPickupLines.getLinesForCategory(category));
    }

    return allLines;
  }

  // Get lines for a specific language only
  static List<String> getAllLinesForLanguage(String language) {
    List<String> allLines = [];
    final categories = getAvailableCategories();

    for (String category in categories) {
      allLines.addAll(getLinesForCategory(category, language));
    }

    return allLines;
  }

  // Get total count of lines
  static int getTotalLinesCount() {
    return getAllLines().length;
  }

  // Get count for specific language
  static int getLinesCountForLanguage(String language) {
    return getAllLinesForLanguage(language).length;
  }

  // Check if a language is supported
  static bool isLanguageSupported(String language) {
    return language.toLowerCase() == 'english' ||
        language.toLowerCase() == 'hindi';
  }

  // Get supported languages
  static List<String> getSupportedLanguages() {
    return ['English', 'Hindi'];
  }
}
