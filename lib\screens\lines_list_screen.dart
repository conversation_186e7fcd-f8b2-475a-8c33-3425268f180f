// lib/screens/lines_list_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../utils/image_cache_manager.dart';
import '../utils/performance_utils.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';

class LinesListScreen extends StatefulWidget {
  final String category;
  const LinesListScreen({super.key, required this.category});

  @override
  State<LinesListScreen> createState() => _LinesListScreenState();
}

class _LinesListScreenState extends State<LinesListScreen> {
  // Background images from 0.png to 10.png
  final List<String> backgroundImages = [
    'assets/images/0.png',
    'assets/images/1.png',
    'assets/images/2.png',
    'assets/images/3.png',
    'assets/images/4.png',
    'assets/images/5.png',
    'assets/images/6.png',
    'assets/images/7.png',
    'assets/images/8.png',
    'assets/images/9.png',
    'assets/images/10.png',
  ];

  // Track background index for each card using ValueNotifiers for better performance
  final Map<int, ValueNotifier<int>> cardBackgroundNotifiers = {};
  DateTime? _lastTapTime; // For throttling rapid taps

  // Sample lines for demonstration - made mutable to allow editing
  List<String> lines = [
    "You're so sweet, you'd put Hershey's out of business!",
    "Are you a 45-degree angle? Because you're a cutie!",
    "Are you a magician? Because whenever I look at you, everyone else disappears.",
    "Let me tie your shoes, cause I don't want you falling for anyone else.",
    "Do you have a map? I keep getting lost in your eyes.",
    "Are you WiFi? Because I'm feeling a connection.",
    "If you were a vegetable, you'd be a cute-cumber!",
    "Are you made of copper and tellurium? Because you're Cu-Te!",
  ];

  void _changeBackground(int index) {
    // Throttle rapid taps to prevent performance issues
    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 200) {
      return; // Ignore rapid taps
    }
    _lastTapTime = now;

    // Play tap sound
    HapticFeedback.lightImpact();

    // Get or create ValueNotifier for this card
    if (!cardBackgroundNotifiers.containsKey(index)) {
      cardBackgroundNotifiers[index] = ValueNotifier<int>(0);
    }

    final notifier = cardBackgroundNotifiers[index]!;
    final currentBg = notifier.value;
    final newBg = (currentBg + 1) % backgroundImages.length;

    // Update only the specific card, not the entire widget
    notifier.value = newBg;
  }

  void _updateQuote(int index, String newQuote) {
    setState(() {
      lines[index] = newQuote;
    });
  }

  @override
  void initState() {
    super.initState();
    // Preload images when this screen loads (lazy loading)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadImagesLazily();
    });
  }

  void _preloadImagesLazily() {
    // Only preload when user actually needs them
    Future.delayed(const Duration(milliseconds: 500), () {
      OptimizedImageCache().preloadBackgroundImages().catchError((error) {
        if (kDebugMode) {
          debugPrint('Background image preload failed: $error');
        }
      });
    });
  }

  @override
  void dispose() {
    // Clean up ValueNotifiers
    for (final notifier in cardBackgroundNotifiers.values) {
      notifier.dispose();
    }
    cardBackgroundNotifiers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: AppBar(
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode
                ? Colors.white
                : Colors.black,
            elevation: 0,
            title: Text(
              widget.category,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: PerformanceUtils.createOptimizedListView(
              padding: EdgeInsets.all(16),
              itemCount: lines.length,
              itemExtent: 404, // Fixed height: 380 + 24 margin
              cacheExtent: 200, // Reduced cache to save memory
              itemBuilder: (context, index) {
                // Lazy initialization of background notifier
                cardBackgroundNotifiers.putIfAbsent(
                  index,
                  () => ValueNotifier<int>(0),
                );

                return QuoteCard(
                  key: ValueKey('quote_card_$index'),
                  index: index,
                  quote: lines[index],
                  backgroundImages: backgroundImages,
                  backgroundNotifier: cardBackgroundNotifiers[index]!,
                  onBackgroundChange: () => _changeBackground(index),
                  onQuoteUpdate: (newQuote) => _updateQuote(index, newQuote),
                );
              },
            ),
          ),
        );
      },
    );
  }
}

class QuoteCard extends StatefulWidget {
  final int index;
  final String quote;
  final List<String> backgroundImages;
  final ValueNotifier<int> backgroundNotifier;
  final VoidCallback onBackgroundChange;
  final Function(String) onQuoteUpdate;

  const QuoteCard({
    super.key,
    required this.index,
    required this.quote,
    required this.backgroundImages,
    required this.backgroundNotifier,
    required this.onBackgroundChange,
    required this.onQuoteUpdate,
  });

  @override
  State<QuoteCard> createState() => _QuoteCardState();
}

class _QuoteCardState extends State<QuoteCard> {
  // Cache expensive decorations to avoid recreating them on every build
  static final _cardDecoration = BoxDecoration(
    borderRadius: BorderRadius.circular(24),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        spreadRadius: 0,
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static final _editButtonDecoration = BoxDecoration(
    color: Colors.white,
    shape: BoxShape.circle,
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.05),
        blurRadius: 2,
        offset: const Offset(0, 1),
      ),
    ],
  );

  static final _borderRadius = BorderRadius.circular(24);
  static final _overlayColor = Colors.black.withValues(alpha: 0.4);

  void _showEditDialog(BuildContext context, int index, String currentQuote) {
    TextEditingController controller = TextEditingController(
      text: currentQuote,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Edit Quote'),
          content: TextField(
            controller: controller,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter your quote...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Update the quote in the parent widget
                String newQuote = controller.text.trim();
                if (newQuote.isNotEmpty) {
                  widget.onQuoteUpdate(newQuote);
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(SnackBar(content: Text('Quote updated!')));
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Quote cannot be empty!')),
                  );
                }
                Navigator.of(context).pop();
              },
              child: Text('Save'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: widget.backgroundNotifier,
      builder: (context, currentBackgroundIndex, child) {
        String currentBg =
            widget.backgroundImages[currentBackgroundIndex %
                widget.backgroundImages.length];

        return Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            final postId = '${widget.index}_${widget.quote.hashCode}';
            final isFavorite = favoritesProvider.isFavorite(postId);

            return GestureDetector(
              onTap: () {
                // Make post reactive - change background and play sound
                widget.onBackgroundChange();
                if (favoritesProvider.tapSoundEnabled) {
                  SystemSound.play(SystemSoundType.click);
                }
              },
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                height: 380, // Optimized height
                decoration: _cardDecoration,
                child: ClipRRect(
                  borderRadius: _borderRadius,
                  child: Stack(
                    children: [
                      // Background Image - Optimized with caching
                      Positioned.fill(
                        child: OptimizedAssetImage(
                          imagePath: currentBg,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.low,
                          errorWidget: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.deepPurple.shade400,
                                  Colors.purple.shade600,
                                  Colors.pink.shade500,
                                ],
                              ),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.favorite,
                                color: Colors.white54,
                                size: 48,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Dark overlay for better text readability
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: _overlayColor,
                      ),
                      // Content
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: widget.onBackgroundChange,
                          child: Container(
                            padding: EdgeInsets.all(12),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                // Edit button (top right)
                                Align(
                                  alignment: Alignment.topRight,
                                  child: GestureDetector(
                                    onTap: () => _showEditDialog(
                                      context,
                                      widget.index,
                                      widget.quote,
                                    ),
                                    child: Container(
                                      width: 36,
                                      height: 36,
                                      decoration: _editButtonDecoration,
                                      child: Icon(
                                        Icons.edit,
                                        color: Colors.black,
                                        size: 18,
                                      ),
                                    ),
                                  ),
                                ),
                                // Quote content
                                Expanded(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 8,
                                    ),
                                    child: Center(
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            height: 1.3,
                                          ),
                                          children: [
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize: 24,
                                                fontWeight: FontWeight.w900,
                                              ),
                                            ),
                                            TextSpan(text: widget.quote),
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize: 24,
                                                fontWeight: FontWeight.w900,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // Action buttons
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 6,
                                    horizontal: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      _buildActionButton(
                                        icon: isFavorite
                                            ? Icons.favorite
                                            : Icons.favorite_border,
                                        label: isFavorite ? 'Liked' : 'Like',
                                        onTap: () {
                                          favoritesProvider.toggleFavorite(
                                            postId,
                                            widget.quote,
                                            currentBg,
                                          );
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                isFavorite
                                                    ? 'Removed from favorites!'
                                                    : 'Added to favorites!',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.download,
                                        label: 'Save',
                                        onTap: () {
                                          // Save image logic
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(content: Text('Saved!')),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.copy,
                                        label: 'Copy',
                                        onTap: () {
                                          Clipboard.setData(
                                            ClipboardData(text: widget.quote),
                                          );
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Copied to clipboard!',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.share,
                                        label: 'Share',
                                        onTap: () {
                                          // Share logic - using basic share for now
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Share functionality',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.black, size: 24),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
